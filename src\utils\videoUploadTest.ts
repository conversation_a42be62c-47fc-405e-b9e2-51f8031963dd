import { supabase } from '../lib/supabase';
import { v4 as uuidv4 } from 'uuid';

export async function testVideoUpload(): Promise<{ success: boolean; error?: string; url?: string }> {
  try {
    // Create a small test blob to simulate a video file
    const testBlob = new Blob(['test video content'], { type: 'video/mp4' });
    const testFile = new File([testBlob], 'test-video.mp4', { type: 'video/mp4' });
    
    const videoFileName = `test-${uuidv4()}-video.mp4`;
    
    console.log('Testing video upload to Supabase storage...');
    
    // Upload the test file
    const { data, error: uploadError } = await supabase.storage
      .from('videos')
      .upload(videoFileName, testFile, {
        cacheControl: '3600',
        upsert: false
      });
    
    if (uploadError) {
      console.error('Upload error:', uploadError);
      return { success: false, error: uploadError.message };
    }

    console.log('File uploaded successfully:', data);

    // Get the public URL
    const { data: { publicUrl }, error: urlError } = supabase.storage
      .from('videos')
      .getPublicUrl(videoFileName);

    if (urlError) {
      console.error('URL error:', urlError);
      return { success: false, error: urlError.message };
    }

    console.log('Public URL generated:', publicUrl);

    // Clean up - delete the test file
    const { error: deleteError } = await supabase.storage
      .from('videos')
      .remove([videoFileName]);

    if (deleteError) {
      console.warn('Failed to clean up test file:', deleteError);
    }

    return { success: true, url: publicUrl };
  } catch (error) {
    console.error('Test failed:', error);
    return { success: false, error: error.message || 'Unknown error' };
  }
}
