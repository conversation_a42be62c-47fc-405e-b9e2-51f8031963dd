import { useState } from 'react';
import { testVideoUpload } from '../utils/videoUploadTest';

export function VideoUploadTest() {
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState<{ success: boolean; error?: string; url?: string } | null>(null);

  const runTest = async () => {
    setTesting(true);
    setResult(null);
    
    try {
      const testResult = await testVideoUpload();
      setResult(testResult);
    } catch (error) {
      setResult({ success: false, error: error.message || 'Test failed' });
    } finally {
      setTesting(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-medium mb-4">Video Upload Test</h3>
      
      <button
        onClick={runTest}
        disabled={testing}
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
      >
        {testing ? 'Testing...' : 'Test Video Upload'}
      </button>

      {result && (
        <div className={`mt-4 p-3 rounded-md ${result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {result.success ? (
            <div>
              <p className="font-medium">✅ Video upload test successful!</p>
              <p className="text-sm mt-1">URL: {result.url}</p>
            </div>
          ) : (
            <div>
              <p className="font-medium">❌ Video upload test failed</p>
              <p className="text-sm mt-1">Error: {result.error}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
