import { supabase } from '../supabaseClient';

// Email configuration
interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

// Email credentials - to be set later
let emailConfig: EmailConfig | null = null;
let adminEmail: string | null = null;

// Set email configuration
export const setEmailConfig = (config: EmailConfig, admin: string) => {
  emailConfig = config;
  adminEmail = admin;
};

// Email data interface
interface EmailData {
  to: string;
  subject: string;
  html: string;
}

// Log email to database
const logEmail = async (
  to: string,
  subject: string,
  bookingId: string | null,
  status: 'success' | 'failed',
  error?: string
) => {
  try {
    const { error: dbError } = await supabase.from('email_logs').insert({
      recipient: to,
      subject,
      booking_id: bookingId,
      status,
      error_message: error,
      sent_at: new Date().toISOString(),
    });

    if (dbError) {
      console.error('Error logging email:', dbError);
    }
  } catch (err) {
    console.error('Error logging email:', err);
  }
};

// Send email function using Supabase Edge Functions or a custom API
export const sendEmail = async (emailData: EmailData, bookingId: string | null = null): Promise<boolean> => {
  if (!emailConfig || !adminEmail) {
    console.error('Email configuration not set');
    return false;
  }

  try {
    const { data, error } = await supabase.functions.invoke('send-email', {
      body: {
        emailData,
        bookingId,
        emailConfig,
        adminEmail
      }
    });

    if (error) throw error;

    // Log successful email
    await logEmail(emailData.to, emailData.subject, bookingId, 'success');

    return data.success;
  } catch (error: any) {
    // Log failed email
    await logEmail(emailData.to, emailData.subject, bookingId, 'failed', error.message);
    console.error('Error sending email:', error);
    return false;
  }
};

// Helper function to get zone display string (similar to Edge Functions)
const getZoneDisplay = (booking: any): string => {
  if (booking.zones?.name) {
    // Direct zone relationship
    return booking.zones.name;
  } else if (booking.booking_zones && booking.booking_zones.length > 0) {
    // Multiple zones through booking_zones
    return booking.booking_zones.map((bz: any) => bz.zones?.name || 'Unknown Zone').join(', ');
  }
  return 'Unknown Zone';
};

// Email templates
export const getBookingApprovedEmailTemplate = (
  businessName: string,
  zoneNames: string,
  startDate: string,
  endDate: string
) => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4c12ff;">Booking Approved</h2>
      <p>Dear Customer,</p>
      <p>We're pleased to inform you that your booking has been approved!</p>

      <div style="background-color: #f3f1ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4c12ff;">Booking Details</h3>
        <p><strong>Business Name:</strong> ${businessName}</p>
        <p><strong>Zone(s):</strong> ${zoneNames}</p>
        <p><strong>Duration:</strong> ${startDate} to ${endDate}</p>
      </div>

      <p>If you have any questions or need further assistance, please don't hesitate to contact us.</p>

      <p>Thank you for choosing our service!</p>

      <p>Best regards,<br>3Shul Admin Team</p>

      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
        <p>This is an automated email. Please do not reply to this message.</p>
      </div>
    </div>
  `;
};

export const getAdminNotificationEmailTemplate = (
  businessName: string,
  zoneNames: string,
  startDate: string,
  endDate: string,
  customerEmail: string
) => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4c12ff;">Booking Approval Notification</h2>
      <p>A booking has been approved:</p>

      <div style="background-color: #f3f1ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4c12ff;">Booking Details</h3>
        <p><strong>Business Name:</strong> ${businessName}</p>
        <p><strong>Zone(s):</strong> ${zoneNames}</p>
        <p><strong>Duration:</strong> ${startDate} to ${endDate}</p>
        <p><strong>Customer Email:</strong> ${customerEmail}</p>
      </div>

      <p>The customer has been notified of this approval.</p>

      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
        <p>This is an automated email. Please do not reply to this message.</p>
      </div>
    </div>
  `;
};

// Email template for booking fulfillment - customer notification
export const getBookingFulfilledEmailTemplate = (
  businessName: string,
  zoneNames: string,
  startDate: string,
  endDate: string
) => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4c12ff;">Campaign Live! 🚀</h2>
      <p>Dear ${businessName},</p>
      <p>Great news! Your advertisement campaign is now live and running!</p>

      <div style="background-color: #f3f1ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4c12ff;">Campaign Details</h3>
        <p><strong>Business Name:</strong> ${businessName}</p>
        <p><strong>Zone(s):</strong> ${zoneNames}</p>
        <p><strong>Campaign Period:</strong> ${startDate} to ${endDate}</p>
        <p><strong>Status:</strong> <span style="color: #3b82f6; font-weight: bold;">Live & Running</span></p>
      </div>

      <p>Your advertisement is now being displayed to thousands of viewers. We hope this campaign brings great results for your business!</p>
      <p>Thank you for choosing 3Shul!</p>

      <p>Best regards,<br>3Shul Team</p>
    </div>
  `;
};

// Email template for admin fulfillment notification
export const getAdminFulfillmentNotificationEmailTemplate = (
  businessName: string,
  zoneNames: string,
  startDate: string,
  endDate: string,
  customerEmail: string
) => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4c12ff;">Booking Fulfillment Notification</h2>
      <p>A booking has been marked as fulfilled and the campaign is now live:</p>

      <div style="background-color: #f3f1ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4c12ff;">Campaign Details</h3>
        <p><strong>Business Name:</strong> ${businessName}</p>
        <p><strong>Zone(s):</strong> ${zoneNames}</p>
        <p><strong>Campaign Period:</strong> ${startDate} to ${endDate}</p>
        <p><strong>Customer Email:</strong> ${customerEmail}</p>
        <p><strong>Status:</strong> <span style="color: #3b82f6; font-weight: bold;">Fulfilled - Live</span></p>
      </div>

      <p>The customer has been notified that their campaign is now live.</p>

      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
        <p>This is an automated email. Please do not reply to this message.</p>
      </div>
    </div>
  `;
};

// Email template for booking cancellation - customer notification
export const getBookingCancelledEmailTemplate = (
  businessName: string,
  zoneNames: string,
  startDate: string,
  endDate: string
) => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #ef4444;">Booking Update</h2>
      <p>Dear ${businessName},</p>
      <p>We regret to inform you that your booking has been cancelled.</p>

      <div style="background-color: #fef2f2; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ef4444;">
        <h3 style="margin-top: 0; color: #ef4444;">Booking Details</h3>
        <p><strong>Business Name:</strong> ${businessName}</p>
        <p><strong>Zone(s):</strong> ${zoneNames}</p>
        <p><strong>Duration:</strong> ${startDate} to ${endDate}</p>
        <p><strong>Status:</strong> <span style="color: #ef4444; font-weight: bold;">Cancelled</span></p>
      </div>

      <p>If you have any questions about this cancellation, please contact our support team. Any payments made will be refunded according to our refund policy.</p>
      <p>We apologize for any inconvenience caused.</p>

      <p>Best regards,<br>3Shul Team</p>
    </div>
  `;
};

// Export the helper function for use in other files
export { getZoneDisplay };

